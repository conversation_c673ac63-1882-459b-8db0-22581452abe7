import streamlit as st
from auth import Auth<PERSON>ana<PERSON>

def show_dashboard():
    """Display the main dashboard after login"""
    auth_manager = AuthManager()
    
    if not auth_manager.is_logged_in():
        st.error("Please log in to access this page.")
        st.stop()
    
    user = st.session_state.user
    
    # Header
    col1, col2 = st.columns([3, 1])
    with col1:
        st.title(f"Welcome, {user['name']}! 👋")
    with col2:
        if st.button("Logout", type="secondary"):
            auth_manager.logout_user()
            st.rerun()
    
    st.divider()
    
    # User info section
    st.subheader("📊 Dashboard Overview")
    
    # Create metrics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Projects", "12", "2")
    with col2:
        st.metric("Active Tasks", "8", "-1")
    with col3:
        st.metric("Completed", "45", "5")
    with col4:
        st.metric("Team Members", "6", "1")
    
    # User profile section
    st.subheader("👤 User Profile")
    col1, col2 = st.columns(2)
    
    with col1:
        st.info(f"""
        **Username:** {user['username']}
        
        **Full Name:** {user['name']}
        
        **Email:** {user['email']}
        
        **Role:** {'Administrator' if user['username'] == 'admin' else 'User'}
        """)
    
    with col2:
        st.subheader("🔧 Quick Actions")
        if st.button("📝 Create New Project", use_container_width=True):
            st.success("New project creation feature coming soon!")
        
        if st.button("📊 View Reports", use_container_width=True):
            st.success("Reports feature coming soon!")
        
        if st.button("⚙️ Settings", use_container_width=True):
            st.success("Settings feature coming soon!")
    
    # Recent activity
    st.subheader("📈 Recent Activity")
    
    activity_data = [
        {"time": "2 hours ago", "action": "Completed task: Update user interface"},
        {"time": "5 hours ago", "action": "Created new project: Mobile App"},
        {"time": "1 day ago", "action": "Reviewed code: Authentication module"},
        {"time": "2 days ago", "action": "Meeting: Team standup"},
        {"time": "3 days ago", "action": "Updated profile information"}
    ]
    
    for activity in activity_data:
        with st.container():
            col1, col2 = st.columns([1, 4])
            with col1:
                st.caption(activity["time"])
            with col2:
                st.write(activity["action"])
    
    # Footer
    st.divider()
    st.caption("🔒 Secure Dashboard - Your data is protected")

if __name__ == "__main__":
    show_dashboard()
