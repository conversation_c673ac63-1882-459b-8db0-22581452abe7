# Streamlit Login System 🔐

A secure and professional login system built with Streamlit, featuring user authentication, session management, and a protected dashboard.

## Features

- 🔑 **Secure Authentication**: Password hashing with bcrypt
- 👤 **User Management**: Login, registration, and session handling
- 🎨 **Modern UI**: Clean and responsive design with custom CSS
- 📊 **Protected Dashboard**: Secure area accessible only after login
- 💾 **Persistent Storage**: User data stored in JSON format
- 🔄 **Session State**: Maintains login state across page refreshes

## Demo Credentials

The application comes with pre-configured demo accounts:

- **Admin Account**: 
  - Username: `admin`
  - Password: `admin123`

- **User Account**:
  - Username: `user`
  - Password: `user123`

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   streamlit run app.py
   ```

4. **Open your browser** and navigate to `http://localhost:8501`

## Project Structure

```
LoginScreen/
├── app.py              # Main Streamlit application
├── auth.py             # Authentication manager
├── dashboard.py        # Protected dashboard page
├── requirements.txt    # Python dependencies
├── users.json          # User database (auto-generated)
└── README.md          # This file
```

## How It Works

### Authentication Flow

1. **Login**: Users enter credentials on the login page
2. **Verification**: Passwords are verified against hashed versions
3. **Session**: Successful login creates a session state
4. **Dashboard**: Authenticated users access the protected dashboard
5. **Logout**: Users can securely log out, clearing session data

### Security Features

- **Password Hashing**: All passwords are hashed using bcrypt
- **Session Management**: Secure session state handling
- **Input Validation**: Form validation and error handling
- **Protected Routes**: Dashboard only accessible when authenticated

## Customization

### Adding New Users

You can register new users through the registration form, or manually add them to the `users.json` file (passwords must be hashed).

### Styling

Modify the CSS in `app.py` to customize the appearance:

```python
st.markdown("""
<style>
    /* Your custom styles here */
</style>
""", unsafe_allow_html=True)
```

### Dashboard Content

Edit `dashboard.py` to customize the dashboard content and add new features.

## Development

### File Descriptions

- **`app.py`**: Main application with login/register forms and routing
- **`auth.py`**: Authentication logic, password hashing, and user management
- **`dashboard.py`**: Protected dashboard with user information and metrics
- **`users.json`**: JSON database storing user credentials (auto-generated)

### Adding Features

1. **Database Integration**: Replace JSON storage with a proper database
2. **Email Verification**: Add email verification for new registrations
3. **Password Reset**: Implement password reset functionality
4. **Role-Based Access**: Add different user roles and permissions
5. **Two-Factor Auth**: Implement 2FA for enhanced security

## Troubleshooting

### Common Issues

1. **Module not found**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **Permission errors**: Ensure the application has write permissions for `users.json`

3. **Port already in use**: Change the port with:
   ```bash
   streamlit run app.py --server.port 8502
   ```

## Security Notes

- This is a demo application for learning purposes
- For production use, consider:
  - Using a proper database instead of JSON files
  - Implementing HTTPS
  - Adding rate limiting
  - Using environment variables for sensitive data
  - Adding proper logging and monitoring

## License

This project is open source and available under the MIT License.
