import json
import bcrypt
import streamlit as st
from typing import Dict, Optional

class AuthManager:
    def __init__(self, users_file: str = "users.json"):
        self.users_file = users_file
        self.users = self.load_users()
    
    def load_users(self) -> Dict:
        """Load users from JSON file"""
        try:
            with open(self.users_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Create default users file if it doesn't exist
            default_users = {
                "admin": {
                    "password": self.hash_password("admin123"),
                    "name": "Administrator",
                    "email": "<EMAIL>"
                },
                "user": {
                    "password": self.hash_password("user123"),
                    "name": "Regular User",
                    "email": "<EMAIL>"
                }
            }
            self.save_users(default_users)
            return default_users
    
    def save_users(self, users: Dict):
        """Save users to JSON file"""
        with open(self.users_file, 'w') as f:
            json.dump(users, f, indent=2)
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def authenticate(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user credentials"""
        if username in self.users:
            user_data = self.users[username]
            if self.verify_password(password, user_data['password']):
                return {
                    'username': username,
                    'name': user_data['name'],
                    'email': user_data['email']
                }
        return None
    
    def register_user(self, username: str, password: str, name: str, email: str) -> bool:
        """Register a new user"""
        if username in self.users:
            return False  # User already exists
        
        self.users[username] = {
            'password': self.hash_password(password),
            'name': name,
            'email': email
        }
        self.save_users(self.users)
        return True
    
    def is_logged_in(self) -> bool:
        """Check if user is logged in"""
        return 'authenticated' in st.session_state and st.session_state.authenticated
    
    def login_user(self, user_data: Dict):
        """Set user as logged in"""
        st.session_state.authenticated = True
        st.session_state.user = user_data
    
    def logout_user(self):
        """Log out user"""
        for key in ['authenticated', 'user']:
            if key in st.session_state:
                del st.session_state[key]
