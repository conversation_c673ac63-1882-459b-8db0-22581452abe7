import streamlit as st
from auth import AuthManager
from dashboard import show_dashboard

# Page configuration
st.set_page_config(
    page_title="Secure Login System",
    page_icon="🔐",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .login-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        background-color: #f8f9fa;
    }
    
    .stButton > button {
        width: 100%;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: bold;
    }
    
    .demo-credentials {
        background-color: #e3f2fd;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #2196f3;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def show_login_page():
    """Display the login page"""
    auth_manager = AuthManager()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🔐 Secure Login System</h1>
        <p>Please enter your credentials to access the dashboard</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Create tabs for Login and Register
    tab1, tab2 = st.tabs(["🔑 Login", "📝 Register"])
    
    with tab1:
        show_login_form(auth_manager)
    
    with tab2:
        show_register_form(auth_manager)

def show_login_form(auth_manager):
    """Display login form"""
    with st.container():
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.markdown('<div class="login-container">', unsafe_allow_html=True)
            
            st.subheader("Login to Your Account")
            
            # Demo credentials info
            st.markdown("""
            <div class="demo-credentials">
                <strong>Demo Credentials:</strong><br>
                👤 <strong>Admin:</strong> admin / admin123<br>
                👤 <strong>User:</strong> user / user123
            </div>
            """, unsafe_allow_html=True)
            
            with st.form("login_form"):
                username = st.text_input("Username", placeholder="Enter your username")
                password = st.text_input("Password", type="password", placeholder="Enter your password")
                
                col1, col2 = st.columns(2)
                with col1:
                    remember_me = st.checkbox("Remember me")
                with col2:
                    forgot_password = st.button("Forgot Password?", type="secondary")
                
                login_button = st.form_submit_button("Login", use_container_width=True)
                
                if login_button:
                    if username and password:
                        user_data = auth_manager.authenticate(username, password)
                        if user_data:
                            auth_manager.login_user(user_data)
                            st.success(f"Welcome back, {user_data['name']}!")
                            st.rerun()
                        else:
                            st.error("Invalid username or password!")
                    else:
                        st.warning("Please enter both username and password!")
                
                if forgot_password:
                    st.info("Password reset feature coming soon! For demo, use the credentials shown above.")
            
            st.markdown('</div>', unsafe_allow_html=True)

def show_register_form(auth_manager):
    """Display registration form"""
    with st.container():
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.markdown('<div class="login-container">', unsafe_allow_html=True)
            
            st.subheader("Create New Account")
            
            with st.form("register_form"):
                new_username = st.text_input("Username", placeholder="Choose a username")
                new_name = st.text_input("Full Name", placeholder="Enter your full name")
                new_email = st.text_input("Email", placeholder="Enter your email address")
                new_password = st.text_input("Password", type="password", placeholder="Choose a password")
                confirm_password = st.text_input("Confirm Password", type="password", placeholder="Confirm your password")
                
                terms_accepted = st.checkbox("I agree to the Terms and Conditions")
                
                register_button = st.form_submit_button("Create Account", use_container_width=True)
                
                if register_button:
                    if not all([new_username, new_name, new_email, new_password, confirm_password]):
                        st.warning("Please fill in all fields!")
                    elif new_password != confirm_password:
                        st.error("Passwords do not match!")
                    elif len(new_password) < 6:
                        st.error("Password must be at least 6 characters long!")
                    elif not terms_accepted:
                        st.warning("Please accept the Terms and Conditions!")
                    else:
                        if auth_manager.register_user(new_username, new_password, new_name, new_email):
                            st.success("Account created successfully! You can now login.")
                        else:
                            st.error("Username already exists! Please choose a different username.")
            
            st.markdown('</div>', unsafe_allow_html=True)

def main():
    """Main application function"""
    auth_manager = AuthManager()
    
    # Check if user is logged in
    if auth_manager.is_logged_in():
        show_dashboard()
    else:
        show_login_page()

if __name__ == "__main__":
    main()
